// import HomepageBanner from "./components/homepageBanner";

import dynamic from 'next/dynamic';
import {Suspense} from "react";

const BlogsSection = dynamic(() => import('./components/blogsSection'), {ssr: false});
const FaqsSection = dynamic(() => import('./components/faqsSection'), {ssr: false});
const HomepageReviewWidget = dynamic(() => import('@/app/hospitalchaintemplate2apollo/components/homepageReviewWidget'), {ssr: false});
const VideosSection = dynamic(() => import('./components/videosSection'), {ssr: false});
const PhotosSection = dynamic(() => import('./components/photosSection'), {ssr: false});
// const WidgetsSection = dynamic(() => import('./components/widgetsSection'), {ssr: false});
import WidgetsSection from './components/widgetsSection';
import CarousalWrapper from "./components/CarousalWrapper";
import HighlightsWidgetChainTemp3 from './components/highlights/highlightsWidgetChainTemp3';
import SpecialityWrapper from './components/SpecialityWrapper';
import DoctorsWrapper from './components/DoctorsWrapper';

const WebsiteStructureDataScript = dynamic(() => import('@/app/commoncomponents/websiteStructureData'), {ssr: false});
const Services = dynamic(() => import('@/app/hospitalchaintemplate2apollo/components/services'), {ssr: false});
const HealthTipSection = dynamic(() => import('@/app/hospitalchaintemplate2apollo/components/healthtips/healthTipSection'), {ssr: false});


export default async function Home() {
    // const websiteData = await getWebsiteData();
    // const {
    //     banners = [],
    //     multiMedia = [],
    //     testimonials = [],
    //     blogs = [],
    //     faqs = [],
    //     websiteServices = []
    // } = websiteData || {};
    // const {enterprise_code: enterpriseCode = null} = websiteData || {};

    return (
        <main>
            <WebsiteStructureDataScript/>
            <Suspense fallback={<div>Loading...</div>}>
                {/* <HomepageBanner/> */}
                <CarousalWrapper/>
            </Suspense>
            <HighlightsWidgetChainTemp3/>
            {/* <WidgetsSection/> */}
            <SpecialityWrapper/>
            <DoctorsWrapper/>
            <PhotosSection/>
            <VideosSection/>
            <HomepageReviewWidget/>
            <HealthTipSection/>
            <Services/>
            <BlogsSection/>
            <FaqsSection/>
        </main>
    );
}
