import { useState } from "react";
import Box from "@mui/material/Box";
import HealthAndSafetyOutlinedIcon from "@mui/icons-material/HealthAndSafetyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import { useRouter } from "next/navigation";
import {Typography} from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SecondNavbarDropdownItem from "@/app/hospitalchaintemplate3/components/navbarSecondDropdownItem";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import Image from "next/image";
import { useTheme } from "@emotion/react";
import { alpha } from "@mui/material/styles";
import { ArrowForwardIos } from "@mui/icons-material";

const NavbarDropdownItem = ({ section = {}, setAnchorEl, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const {
    displayName = "",
    sections = [],
    redirection = {},
    iconUrl = "",
    type = 1,
  } = section || {};

  const handleClick = () => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setIsOpen((prev) => !prev);
  };

  return (
    <Box>
      <Box
          sx={{
              display: "flex",
              alignItems: "center",
              fontSize: "14px",
              gap: "12px",
              cursor: "pointer",
              padding: "12px 16px",
              justifyContent: "space-between",
              margin: "0 8px",
              borderRadius: "8px",
              transition: "all 0.3s ease",
              "&:hover": {
                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  color: "primary.main",
                  transform: "translateX(4px)"
              },
          }}
        onClick={handleClick}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
            {
             iconUrl ?
                 <Box sx={{
                   display: "flex",
                   alignItems: "center",
                   justifyContent: "center",
                   width: "24px",
                   height: "24px",
                   borderRadius: "4px",
                   backgroundColor: alpha(theme.palette.primary.main, 0.1)
                 }}>
                   <Image
                       alt="icon"
                       src={getThumborUrl(iconUrl)}
                       width={16}
                       height={16}
                   />
                 </Box>
                 : <KeyboardArrowDownOutlinedIcon
                 sx={{ color: "#333", rotate: "-90deg" }}
             />
            }
            <Typography
                fontSize="14px"
                sx={{
                  fontWeight: 500,
                  color: "inherit"
                }}
            >
                {displayName || ""}
            </Typography>
        </Box>
        {sections !== null && (
          <KeyboardArrowDownOutlinedIcon
            sx={{
              fontSize: "18px",
              color: theme.palette.primary.main,
              opacity: 0.7,
              transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.3s ease"
            }}
          />
        )}
      </Box>
      {sections !== null &&
        isOpen &&
        sections.map((section, index) => {
          const { displayName = "" } = section || {};
          return (
            <SecondNavbarDropdownItem
              key={`${displayName}${index}`}
              section={section}
              setAnchorEl={setAnchorEl}
            />
          );
        })}
    </Box>
  );
};

export default NavbarDropdownItem;
