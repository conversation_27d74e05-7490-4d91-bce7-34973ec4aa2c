
"use client";

import Image from "next/image";
import Link from "next/link";
import {
  Box,
  Divider,
  Typography,
  alpha,
  Skeleton,
} from "@mui/material";
// import BloodtypeOutlinedIcon from "@mui/icons-material/BloodtypeOutlined";
import NavbarDropdown from "./components/navbarDropdown";
import NavbarMenu from "./components/navbarMenu";
// import CallUsButton from "./components/callUsButton";
import { useContext, useEffect, useState } from "react";
import { getNavbarItems } from "@/api/harbor.service";
import { AppContext } from "../AppContextLayout";
import { getThumborUrl } from "../utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import BottomNavBar from "@/app/commoncomponents/bottomNavBar";

const Navbar = () => {
  const theme = useTheme();
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, logo_url: logoUrl = "", phoneNumbers = [] } = websiteData || {};
  const primaryPhone = phoneNumbers?.[0]?.phone || ""

  const fetchNavbarItems = async () => {
    setIsLoading(true);
    try {
      const response = await getNavbarItems(enterpriseCode);
      const { result = [] } = response || {};
      setNavbarItemsList(result);
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) fetchNavbarItems();
  }, [enterpriseCode]);

  return (
    <>
      <Box
        sx={{
          position: "sticky",
          top: { xs: 0, md: "-36px" },
          zIndex: 10,
        }}
      >
        <Box
          sx={{
            alignItems: "center",
            justifyContent: "flex-end",
            boxShadow: "0px 1px 1px #00000014",
            padding: { xs: "8px 16px", md: "8px 80px", lg: "8px 100px" },
            borderBottom: "1px solid #00000014",
            display: { xs: "none", md: "flex" },
            // display: "none"
          }}
        >
          <Box
            sx={{
              color: "primary.main",
              fontSize: "14px",
              listStyle: "none",
              display: "flex",
              alignItems: "center",
              gap: "24px",
              zIndex: 10,
            }}
          >
              {isLoading
                  ? // Show Skeleton items while loading
                  Array.from({ length: 4 }).map((_, index) => (
                      <Skeleton
                          key={index}
                          variant="text"
                          width={80}
                          height={17}
                          sx={{ borderRadius: "4px" }}
                      />
                  ))
                  : // Render actual navbar items when data is ready
                  (navbarItemsList[0]?.sections || []).map((item, index) => {
                      const {
                          displayName = "",
                          redirection = {},
                          sections = null,
                          type = 1,
                      } = item || {};
                      const { redirectionUrl = "" } = redirection || {};
                      if (type === 2)
                          return (
                              <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                                  <Link href={redirectionUrl} target="_blank" underline="none">
                                      <Typography
                                          fontSize="14px"
                                      >
                                          {displayName || ""}
                                      </Typography>
                                  </Link>
                              </Box>
                          );
                      else if (sections)
                          return (
                              <NavbarDropdown
                                  key={`${displayName}${index}`}
                                  navbarItem={item}
                                  id={`navbarSection0Item${index}`}
                              />
                          );
                      else
                          return (
                              <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                                  {/*<Link href={redirectionUrl}>{displayName || ""}</Link>*/}
                                  <Link href={redirectionUrl} target="_blank" underline="none">
                                      <Typography
                                          fontSize="14px"
                                      >
                                          {displayName || ""}
                                      </Typography>
                                  </Link>
                              </Box>
                          );
                  })}
          </Box>
        </Box>

          <Box sx={{
              display: "flex",
              background: "#fff",
              justifyContent: "space-between",
              paddingX: { xs: "16px", md: "80px", lg: "100px" },
              alignItems: "center",
              borderBottom: "1px solid #00000014",
          }}>
              <Link id="navbarLogo" href="/">
                  {logoUrl ?
                      <Box sx={{
                          display: {xs: "none", md: "block"}
                      }}>
                          <Image
                              alt="logo"
                              src={getThumborUrl(logoUrl)}
                              width={240}
                              height={80}
                          />
                      </Box>
                      : <Typography variant="h4">LOGO</Typography>}
                  {logoUrl ?
                      <Box sx={{
                          display: {xs: "block", md: "none"}
                      }}>
                          <Image
                              alt="logo"
                              src={getThumborUrl(logoUrl)}
                              width={210}
                              height={70}
                          />
                      </Box>
                      : <Typography variant="h4">LOGO</Typography>}
              </Link>
              <Box
                  sx={{
                      display: "flex",
                      alignItems: "center",
                      padding: { xs: "8px 16px", md: "8px 80px", lg: "8px 100px" },
                      justifyContent: "space-between",
                      background: "#fff",
                  }}
              >


                  <Box sx={{ display: { xs: "none", md: "block" } }}>
                      <Box
                          sx={{
                              color: "primary.main",
                              listStyle: "none",
                              display: "flex",
                              alignItems: "center",
                              gap: "24px",
                          }}
                      >
                          {isLoading
                              ? // Show Skeleton items while loading
                              Array.from({ length: 4 }).map((_, index) => (
                                  <Skeleton
                                      key={index}
                                      variant="text"
                                      width={80}
                                      height={20}
                                      sx={{ borderRadius: "4px" }}
                                  />
                              ))
                              : // Render actual navbar items when data is ready
                              (navbarItemsList[1]?.sections || []).map((item, index) => {
                                  const {
                                      displayName = "",
                                      redirection = {},
                                      sections = null,
                                      type = 1,
                                  } = item || {};
                                  const { redirectionUrl = "" } = redirection || {};
                                  if (type === 2)
                                      return (
                                          <Box id={`navbarSection1Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                                              <Link href={redirectionUrl} target="_blank">
                                                  <Typography
                                                      fontSize="14px"
                                                  >
                                                      {displayName || ""}
                                                  </Typography>
                                              </Link>
                                          </Box>
                                      );
                                  else if (sections)
                                      return (
                                          <NavbarDropdown
                                              key={`${displayName}${index}`}
                                              navbarItem={item}
                                              id={`navbarSection0Item${index}`}
                                          />
                                      );
                                  else
                                      return (
                                          <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                                              <Link href={redirectionUrl}><Typography
                                                  fontSize="14px"
                                              >
                                                  {displayName || ""}
                                              </Typography></Link>
                                          </Box>
                                      );
                              })
                              }
                      </Box>
                  </Box>

              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: "16px" }}>
                {/* Phone Number - Full on Large Desktop */}
                <Box
                  sx={{
                    display: { xs: "none", lg: "flex" },
                    alignItems: "center",
                    gap: "8px",
                    border: `2px solid ${theme.palette.primary.main}`,
                    borderRadius: "8px",
                    padding: "8px 16px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1)
                    }
                  }}
                >
                  <Box
                    sx={{
                      color: theme.palette.primary.main,
                      display: "flex",
                      alignItems: "center"
                    }}
                  >
                    📞
                  </Box>
                  <Typography
                    sx={{
                      color: theme.palette.primary.main,
                      fontSize: "14px",
                      fontWeight: 500
                    }}
                  >
                    {primaryPhone}
                  </Typography>
                </Box>

                {/* Call Icon + Number - Tablet */}
                <Box
                  sx={{
                    display: { xs: "none", md: "flex", lg: "none" },
                    alignItems: "center",
                    gap: "8px",
                    border: `2px solid ${theme.palette.primary.main}`,
                    borderRadius: "8px",
                    padding: "8px 12px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1)
                    }
                  }}
                >
                  <Box
                    sx={{
                      color: theme.palette.primary.main,
                      display: "flex",
                      alignItems: "center"
                    }}
                  >
                    📞
                  </Box>
                  <Typography
                    sx={{
                      color: theme.palette.primary.main,
                      fontSize: "13px",
                      fontWeight: 500
                    }}
                  >
                    {primaryPhone}
                  </Typography>
                </Box>

                {/* Call Icon Only - Mobile */}
                <Box
                  sx={{
                    display: { xs: "flex", md: "none" },
                    alignItems: "center",
                    justifyContent: "center",
                    border: `2px solid ${theme.palette.primary.main}`,
                    color: "white",
                    width: "40px",
                    height: "40px",
                    borderRadius: "8px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                  }}
                >
                  📞
                </Box>

                {/* Contact Us Button - Large Desktop Only */}
                <Box
                  sx={{
                    display: { xs: "none", lg: "block" },
                    backgroundColor: theme.palette.primary.main,
                    color: "white",
                    padding: "10px 20px",
                    borderRadius: "8px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "14px",
                      fontWeight: 500
                    }}
                  >
                    Contact Us
                  </Typography>
                </Box>
                  <Divider
                      orientation="vertical"
                      flexItem
                      sx={{ display: { xs: "inline-block", md: "none" } }}
                  />
                  <NavbarMenu navbarItemsList={navbarItemsList} />
              </Box>
          </Box>
      </Box>
        <BottomNavBar/>
    </>
  );
};

export default Navbar;
