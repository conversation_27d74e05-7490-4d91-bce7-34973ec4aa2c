import apiClient from "@/api/api.client";

export const getHomeComponentsData = async (params, widgetType) => {
  try {
    const response = await apiClient.get(
      `/api/v1/public/website/website-data/${widgetType}`,
      { params }
    );
    return response.data;
  } catch (error) {
    console.log("getHomeComponentsData", error);
  }
};

export const getHomeSectionHeadings = async (params, sectionType) => {
  try {
    const response = await apiClient.get(
      `/api/v1/public/website/website-data/template-component/${sectionType}`,
      { params }
    );
    // console.log(response)
    return response.data;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};


export const fetchWebsiteData = async (params) => {
  try {
    const response = await apiClient.get(`/api/v1/public/website/`, { params });
    return response.data;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};
export const getProceduresList = async (enterpriseCode, params) => {
  try {
    const response = await apiClient.get(
      `/api/v1/public/enterprise/${enterpriseCode}/procedure/`,
      { params }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching procedures", error);
    throw error;
  }
};
export const getNavbarItems = async (enterpriseCode) => {
  if (!enterpriseCode) return [];
  try {
    const response = await apiClient.get(
      `/api/v1/public/website/navbar/${enterpriseCode}/`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting Navbar items", error);
    throw error;
  }
};

export const getProcedures = async (enterpriseCode) => {
  if (!enterpriseCode) return [];
  try {
    const response = await apiClient.get(
      `/api/v1/public/enterprise/${enterpriseCode}/procedure/?list=true`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting procedure items", error);
    throw error;
  }
};
export const getDoctors = async (enterpriseCode) => {
  if (!enterpriseCode) return [];
  try {
    const response = await apiClient.get(
      `/api/v1/public/doctors/?enterpriseCode=${enterpriseCode}`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting procedure items", error);
    throw error;
  }
};

export const getSpecialities = async (enterpriseCode) => {
  if (!enterpriseCode) return [];
  try {
    const response = await apiClient.get(
      `/api/v1/public/enterprise/${enterpriseCode}/speciality/?list=true`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting specialities", error);
    throw error;
  }
};

// Lab-related API functions
export const getLabCategories = async (enterpriseCode, params = {}) => {
  if (!enterpriseCode) return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/category`,
      {
        params: {
          enterpriseCode,
          list: true,
          view: 'basic',
          ...params
        }
      }
    );
    return response.data?.result || { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  } catch (error) {
    console.error("Error fetching lab categories:", error);
    return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  }
};

export const getLabCategoryByCode = async (enterpriseCode, code, params = {}) => {
  if (!enterpriseCode || !code) return null;
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/category`,
      {
        params: {
          enterpriseCode,
          code,
          view: 'detailed',
          ...params
        }
      }
    );
    return response.data?.result || null;
  } catch (error) {
    console.error("Error fetching lab category details:", error);
    return null;
  }
};

export const getLabTests = async (enterpriseCode, params = {}) => {
  if (!enterpriseCode) return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/test`,
      {
        params: {
          enterpriseCode,
          list: true,
          view: 'basic',
          ...params
        }
      }
    );
    return response.data?.result || { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  } catch (error) {
    console.error("Error fetching lab tests:", error);
    return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  }
};

export const getLabTestByCode = async (enterpriseCode, code) => {
  if (!enterpriseCode || !code) return null;
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/test`,
      {
        params: {
          enterpriseCode,
          code,
          view: 'detailed'
        }
      }
    );
    return response.data?.result || null;
  } catch (error) {
    console.error("Error fetching lab test details:", error);
    return null;
  }
};

export const getLabPackages = async (enterpriseCode, params = {}) => {
  if (!enterpriseCode) return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/package`,
      {
        params: {
          enterpriseCode,
          list: true,
          view: 'basic',
          ...params
        }
      }
    );
    return response.data?.result || { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  } catch (error) {
    console.error("Error fetching lab packages:", error);
    return { data: [], totalCount: 0, currentPage: 1, totalPages: 0 };
  }
};

export const getLabPackageByCode = async (enterpriseCode, code) => {
  if (!enterpriseCode || !code) return null;
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/package`,
      {
        params: {
          enterpriseCode,
          code,
          view: 'detailed'
        }
      }
    );
    return response.data?.result || null;
  } catch (error) {
    console.error("Error fetching lab package details:", error);
    return null;
  }
};

export const searchLabs = async (enterpriseCode, query) => {
  if (!enterpriseCode || !query) return { tests: [], packages: [] };
  try {
    const response = await apiClient.get(
      `/api/v1/public/lab/search`,
      {
        params: {
          enterpriseCode,
          list: true,
          q: query
        }
      }
    );
    return response.data?.result || { tests: [], packages: [] };
  } catch (error) {
    console.error("Error searching labs:", error);
    return { tests: [], packages: [] };
  }
};
