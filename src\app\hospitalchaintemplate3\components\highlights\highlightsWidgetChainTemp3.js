"use client";

import {Box} from "@mui/material";
import {useContext, useEffect, useState} from "react";
import {AppContext} from "@/app/AppContextLayout";
import WebHighlightsWidgetChainTemp3
    from "@/app/hospitalchaintemplate3/components/highlights/webHighlightsWidgetChainTemp3";
import MWebHighlightsWidgetChainTemp3
    from "@/app/hospitalchaintemplate3/components/highlights/MWebHighlightsWidgetChainTemp3";
import {getHomeSectionHeadings} from "@/api/harbor.service";
import {HOME_SECTION_HEADING_TYPE} from "@/constants";
import {getWebsiteHost} from "@/app/utils/clientOnly/clientUtils";

const HighlightsWidgetChainTemp3 = () => {
    const {websiteData = {},} = useContext(AppContext);
    const {highlights = []} = websiteData || {};
    const [headings, setHeadings] = useState([]);

    const fetchHighlightsHeadings = async () => {
        try {
            const domainName = getWebsiteHost();
            const data = await getHomeSectionHeadings(
                { domainName: domainName },
                HOME_SECTION_HEADING_TYPE.HIGHLIGHTS
            );
            if (data?.code === 200) {
                setHeadings(data?.result || []);
            }
        } catch (error) {
            console.error("Error fetching highlights headings:", error);
        }
    };

    useEffect(() => {
        fetchHighlightsHeadings();
    }, []);

    return (
        <>
            {highlights.length > 0 && (
                <Box
                    sx={{
                        position: "relative",
                        // width: "max-content",
                        zIndex: 1,
                        display: {xs: "none", md: "block"},
                    }}
                >
                    <WebHighlightsWidgetChainTemp3 highlights={highlights} headings={headings}/>
                </Box>
            )}
            <Box
                sx={{
                    position: "relative",
                    display: {xs: "block", md: "none"},
                }}
            >
                {highlights.length > 0 && (
                        <MWebHighlightsWidgetChainTemp3 highlights={highlights}/>
                    // </SectionLayoutChainTemp2>
                )}
            </Box>
        </>
    );
};

export default HighlightsWidgetChainTemp3;
