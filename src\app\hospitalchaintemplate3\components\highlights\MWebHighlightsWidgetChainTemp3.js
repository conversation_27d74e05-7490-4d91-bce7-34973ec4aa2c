"use client";

import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";

const MWebHighlightsWidgetChainTemp3 = ({ highlights }) => {
  const router = useRouter();
  const theme = useTheme();

  const onhighlightItemClick = (type, url) => {
    if (type === 0) {
      // Internal redirect
      router.push(url);
    } else if (type === 1) {
      // External redirect, open in a new tab
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  return (
    <Box
      sx={{
        backgroundColor: theme.palette.primary.main,
        padding: "20px 16px",
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        position: "relative",
        width: "100%",
      }}
    >
      {highlights.map((highlightItem) => {
        const {
          code = null,
          title: displayName = "",
          imageUrl: iconUrl = "",
          highlightsRedirection = {},
          shortDescription = ""
        } = highlightItem || {};
        const { redirectionUrl: url, redirectionType: type } =
          highlightsRedirection;
        return (
          <Box
            id={`highlightsCard-${code}`}
            key={code}
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              cursor: "pointer",
              padding: "16px",
              transition: "all 0.3s ease",
              borderRadius: "12px",
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              width: "100%",
              gap: "16px",
              ":hover": {
                backgroundColor: `rgba(255, 255, 255, 0.15)`,
                transform: "translateY(-1px)",
                boxShadow: "0 4px 16px rgba(0,0,0,0.2)",
                borderColor: "rgba(255, 255, 255, 0.2)",
              }
            }}
            onClick={() => onhighlightItemClick(type, url)}
          >
            {/* Icon */}
            <Box sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              minWidth: "48px",
              height: "48px",
              borderRadius: "12px",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            }}>
              {iconUrl ? (
                <Image
                  alt="service-icon"
                  src={getThumborUrl(iconUrl, 28, 28)}
                  height={28}
                  width={28}
                  style={{ filter: "brightness(0) invert(1)" }}
                />
              ) : (
                <MedicationIcon
                  sx={{ fontSize: "28px", color: "white" }}
                />
              )}
            </Box>

            {/* Text Content */}
            <Box sx={{ flex: 1, display: "flex", flexDirection: "column", gap: "4px" }}>
              {/* Title */}
              <Typography
                sx={{
                  fontWeight: 600,
                  color: "white",
                  fontSize: "16px",
                  textAlign: "left",
                  lineHeight: 1.3,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "-webkit-box",
                  WebkitLineClamp: 1,
                  WebkitBoxOrient: "vertical",
                }}
              >
                {displayName || ""}
              </Typography>

              {/* Description */}
              {shortDescription && (
                <Typography
                  sx={{
                    color: "white",
                    opacity: 0.85,
                    fontSize: "13px",
                    textAlign: "left",
                    lineHeight: 1.4,
                    fontWeight: 400,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                  }}
                >
                  {shortDescription}
                </Typography>
              )}
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export default MWebHighlightsWidgetChainTemp3;
