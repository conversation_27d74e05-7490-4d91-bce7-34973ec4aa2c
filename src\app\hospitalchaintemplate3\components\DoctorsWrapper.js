"use client";

import { Box, Typography, IconButton } from "@mui/material";
import { useContext, useEffect, useState, useRef } from "react";
import { AppContext } from "../../AppContextLayout";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import Doctors from "./Doctors";
import { ArrowRightAlt, ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import { getHomeSectionHeadings, getDoctors } from "@/api/harbor.service";
import { getWebsiteHost } from "@/app/utils/clientOnly/clientUtils";
import { HOME_SECTION_HEADING_TYPE } from "@/constants";

const DoctorsWrapper = () => {
  const [doctors, setDoctors] = useState([]);
  const [headings, setHeadings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const { websiteData = {} } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, location_code: locationCode = null } =
    websiteData || {};
  const router = useRouter();
  const theme = useTheme();
  const swiperRef = useRef(null);

  const fetchDoctors = async () => {
    setIsLoading(true);
    try {
      const response = await getDoctors(enterpriseCode);
      const { result = {} } = response || {};
      const { doctors = [] } = result || {};
      setDoctors(doctors);
    } catch (error) {
      console.error("Error fetching doctors:", error);
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDoctorsHeadings = async () => {
    try {
      const domainName = getWebsiteHost();
      const data = await getHomeSectionHeadings(
        { domainName: domainName },
        HOME_SECTION_HEADING_TYPE.DOCTORS
      );
      if (data?.code === 200) {
        setHeadings(data?.result || []);
      }
    } catch (error) {
      console.error("Error fetching doctors headings:", error);
    }
  };

  useEffect(() => {
    if (enterpriseCode) fetchDoctors();
    fetchDoctorsHeadings();
  }, [enterpriseCode]);

  if (!doctors.length && !isLoading) {
    return null;
  }

  const handleViewAllClick = () => {
    router.push(`/doctors`);
  };

  const handlePrevSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  return (
    <Box
      sx={{
        padding: { xs: "16px 16px", sm: "24px 16px", md: "32px 80px", lg: "48px 100px" },
        backgroundColor: theme.palette.primary.main,
        overflow: "hidden", // Prevent card cutting
      }}
    >
      <Box sx={{ maxWidth: "1400px", margin: "0 auto" }}>
        {/* Header Section */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: { xs: "flex-start", sm: "flex-end" },
            marginBottom: "40px",
            flexDirection: { xs: "column", sm: "row" },
            gap: { xs: "16px", sm: "24px" },
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 500,
                fontSize: { xs: "24px", sm: "28px", md: "32px" },
                color: "#fff",
                marginBottom: "8px",
              }}
            >
              {headings[0]?.heading || "Our Doctors"}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "rgba(255, 255, 255, 0.8)",
                fontSize: "14px",
                maxWidth: "600px",
                lineHeight: 1.5,
              }}
            >
              {headings[0]?.subHeading || "Meet our experienced team of medical professionals dedicated to providing exceptional healthcare."}
            </Typography>
          </Box>
          <Typography
            variant="body2"
            sx={{
              color: "#fff",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              fontSize: "16px",
              fontWeight: 500,
              alignSelf: { xs: "flex-start", sm: "auto" }, // Align to start on mobile
              "&:hover": {
                color: "rgba(255, 255, 255, 0.8)",
              },
            }}
            onClick={handleViewAllClick}
          >
            View All <ArrowRightAlt />
          </Typography>
        </Box>

        <Doctors
          doctors={doctors}
          isLoading={isLoading}
          locationCode={locationCode}
          swiperRef={swiperRef}
          selectedLocation={selectedLocation}
        />

        {/* Navigation Buttons */}
        {!isLoading && doctors.length > 0 && (
          <Box
            sx={{
              display: "flex",
              gap: "8px",
              marginTop: "20px",
              justifyContent: "flex-start",
            }}
          >
            <IconButton
              onClick={handlePrevSlide}
              sx={{
                width: "40px",
                height: "40px",
                borderRadius: "4px", // Square shape instead of circle
                backgroundColor: "#fff",
                color: theme.palette.primary.main,
                border: `2px solid #fff`,
                "&:hover": {
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: `2px solid #fff`,
                },
              }}
            >
              <ArrowBackIos sx={{ fontSize: "18px", marginLeft: "4px" }} />
            </IconButton>
            <IconButton
              onClick={handleNextSlide}
              sx={{
                width: "40px",
                height: "40px",
                borderRadius: "4px", // Square shape instead of circle
                backgroundColor: "#fff",
                color: theme.palette.primary.main,
                border: `2px solid #fff`,
                "&:hover": {
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: `2px solid #fff`,
                },
              }}
            >
              <ArrowForwardIos sx={{ fontSize: "18px" }} />
            </IconButton>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default DoctorsWrapper;
